#!/bin/bash

# A100优化的训练启动脚本
# 专门针对NVIDIA A100 GPU优化的配置

echo "=========================================="
echo "A100优化训练脚本启动"
echo "=========================================="

# 检测GPU信息
echo "检测GPU信息..."
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits

# 检查是否为A100
GPU_NAME=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
if [[ $GPU_NAME == *"A100"* ]]; then
    echo "✓ 检测到A100 GPU: $GPU_NAME"
    echo "✓ 应用A100专用优化配置"
else
    echo "⚠ 警告: 未检测到A100 GPU，当前GPU: $GPU_NAME"
    echo "⚠ 脚本仍会运行，但可能无法获得最佳性能"
fi

# 设置A100优化的环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TORCH_CUDNN_BENCHMARK=1
export TORCH_CUDNN_DETERMINISTIC=0
export NVIDIA_TF32_OVERRIDE=1
export TORCH_ALLOW_TF32_CUBLAS_OVERRIDE=1

echo "✓ A100优化环境变量已设置"

# A100优化的训练参数
BATCH_SIZE=8              # A100优化：更大的批次大小
EVAL_BATCH_SIZE=16        # A100优化：更大的评估批次大小
MAX_SEQ_LEN=1024          # A100优化：更长的序列长度
GRADIENT_ACCUMULATION=2   # A100优化：减少梯度累积步数
LEARNING_RATE=2e-5        # A100优化：稍高的学习率
LR2=1.5e-4               # A100优化：verbalizer学习率
WARMUP_RATIO=0.1         # A100优化：warmup比例
WEIGHT_DECAY=0.01        # A100优化：权重衰减

# 精度设置 - A100原生支持BF16
USE_BF16="--use_bf16"
FORCE_BF16="--force_bf16"
USE_AMP="--use_amp"

# 数据集和模型路径
DATASET="wos"
MODEL_PATH="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B"
RESULT_FILE="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train_a100.txt"

# 其他优化参数
SHOT=30
SEED=171
MAX_EPOCHS=1
DEVICE=0

echo "=========================================="
echo "A100优化训练参数:"
echo "  批次大小: $BATCH_SIZE"
echo "  评估批次大小: $EVAL_BATCH_SIZE"
echo "  最大序列长度: $MAX_SEQ_LEN"
echo "  梯度累积步数: $GRADIENT_ACCUMULATION"
echo "  学习率: $LEARNING_RATE"
echo "  Verbalizer学习率: $LR2"
echo "  精度: BF16 (A100原生支持)"
echo "  数据集: $DATASET"
echo "=========================================="

# 切换到DCL目录
cd /home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL

# 启动训练
echo "开始A100优化训练..."
python train_tb.py \
    --model qwen3 \
    --model_name_or_path "$MODEL_PATH" \
    --result_file "$RESULT_FILE" \
    --dataset "$DATASET" \
    --batch_size $BATCH_SIZE \
    --eval_batch_size $EVAL_BATCH_SIZE \
    --max_seq_lens $MAX_SEQ_LEN \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION \
    --lr $LEARNING_RATE \
    --lr2 $LR2 \
    --warmup_ratio $WARMUP_RATIO \
    --weight_decay $WEIGHT_DECAY \
    --shot $SHOT \
    --seed $SEED \
    --max_epochs $MAX_EPOCHS \
    --device $DEVICE \
    $USE_BF16 \
    $FORCE_BF16 \
    $USE_AMP \
    --use_scheduler1 \
    --use_scheduler2 \
    --contrastive_loss 1 \
    --contrastive_alpha 1.0 \
    --constraint_loss 1 \
    --constraint_alpha 0.1 \
    --lm_training 1 \
    --lm_alpha 0.5 \
    --multi_verb 1 \
    --depth 7 \
    --dropout 0.1 \
    --max_grad_norm 1.0 \
    --early_stop 10 \
    --eval_full 0

echo "=========================================="
echo "A100优化训练完成!"
echo "结果文件: $RESULT_FILE"
echo "TensorBoard日志: runs/train/"
echo "=========================================="

# 显示最终GPU内存使用情况
echo "最终GPU内存使用情况:"
nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits
