#!/bin/bash

# 3090 vs A100 性能对比脚本
# 用于对比3090和A100的训练性能差异

echo "=========================================="
echo "3090 vs A100 性能对比测试"
echo "=========================================="

# 检测当前GPU
GPU_NAME=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)

echo "当前GPU: $GPU_NAME"
echo "GPU内存: ${GPU_MEMORY}MB"

# 根据GPU类型选择配置
if [[ $GPU_NAME == *"A100"* ]]; then
    echo "✓ 检测到A100，使用A100优化配置"
    CONFIG_TYPE="A100"
    BATCH_SIZE=8
    EVAL_BATCH_SIZE=16
    MAX_SEQ_LEN=1024
    GRADIENT_ACCUMULATION=2
    LEARNING_RATE=2e-5
    LR2=1.5e-4
    USE_BF16="--use_bf16 --force_bf16"
    USE_AMP="--use_amp"
    MEMORY_FRACTION=0.95
elif [[ $GPU_NAME == *"3090"* ]] || [[ $GPU_NAME == *"RTX"* ]]; then
    echo "✓ 检测到RTX系列GPU，使用3090兼容配置"
    CONFIG_TYPE="RTX_3090"
    BATCH_SIZE=2
    EVAL_BATCH_SIZE=3
    MAX_SEQ_LEN=512
    GRADIENT_ACCUMULATION=4
    LEARNING_RATE=1.5e-5
    LR2=1e-4
    USE_BF16=""
    USE_AMP=""
    MEMORY_FRACTION=0.85
else
    echo "⚠ 未识别的GPU类型，使用保守配置"
    CONFIG_TYPE="UNKNOWN"
    BATCH_SIZE=1
    EVAL_BATCH_SIZE=2
    MAX_SEQ_LEN=512
    GRADIENT_ACCUMULATION=8
    LEARNING_RATE=1e-5
    LR2=5e-5
    USE_BF16=""
    USE_AMP=""
    MEMORY_FRACTION=0.8
fi

# 设置环境变量
if [[ $CONFIG_TYPE == "A100" ]]; then
    # A100优化环境变量
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    export TORCH_CUDNN_BENCHMARK=1
    export TORCH_CUDNN_DETERMINISTIC=0
    export NVIDIA_TF32_OVERRIDE=1
    export TORCH_ALLOW_TF32_CUBLAS_OVERRIDE=1
else
    # 3090兼容环境变量
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
    export TORCH_CUDNN_BENCHMARK=0
    export TORCH_CUDNN_DETERMINISTIC=1
fi

# 通用参数
DATASET="wos"
MODEL_PATH="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B"
RESULT_FILE="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/performance_comparison_${CONFIG_TYPE}.txt"
SHOT=30
SEED=171
MAX_EPOCHS=1
DEVICE=0

echo "=========================================="
echo "配置类型: $CONFIG_TYPE"
echo "批次大小: $BATCH_SIZE"
echo "评估批次大小: $EVAL_BATCH_SIZE"
echo "最大序列长度: $MAX_SEQ_LEN"
echo "梯度累积步数: $GRADIENT_ACCUMULATION"
echo "学习率: $LEARNING_RATE"
echo "精度设置: ${USE_BF16:-FP32/FP16}"
echo "内存分配: ${MEMORY_FRACTION}%"
echo "=========================================="

# 记录开始时间
START_TIME=$(date +%s)
echo "开始时间: $(date)"

# 切换到DCL目录
cd /home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL

# 启动训练
echo "开始训练..."
python train_tb.py \
    --model qwen3 \
    --model_name_or_path "$MODEL_PATH" \
    --result_file "$RESULT_FILE" \
    --dataset "$DATASET" \
    --batch_size $BATCH_SIZE \
    --eval_batch_size $EVAL_BATCH_SIZE \
    --max_seq_lens $MAX_SEQ_LEN \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION \
    --lr $LEARNING_RATE \
    --lr2 $LR2 \
    --warmup_ratio 0.1 \
    --weight_decay 0.01 \
    --shot $SHOT \
    --seed $SEED \
    --max_epochs $MAX_EPOCHS \
    --device $DEVICE \
    $USE_BF16 \
    $USE_AMP \
    --use_scheduler1 \
    --use_scheduler2 \
    --contrastive_loss 1 \
    --contrastive_alpha 1.0 \
    --constraint_loss 1 \
    --constraint_alpha 0.1 \
    --lm_training 1 \
    --lm_alpha 0.5 \
    --multi_verb 1 \
    --depth 7 \
    --dropout 0.1 \
    --max_grad_norm 1.0 \
    --early_stop 10 \
    --eval_full 0

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "=========================================="
echo "训练完成!"
echo "结束时间: $(date)"
echo "总耗时: ${DURATION}秒 ($(($DURATION / 60))分钟)"
echo "配置类型: $CONFIG_TYPE"
echo "结果文件: $RESULT_FILE"
echo "=========================================="

# 生成性能报告
echo "生成性能报告..."
cat > "/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/performance_report_${CONFIG_TYPE}.txt" << EOF
========================================
性能测试报告 - $CONFIG_TYPE
========================================
GPU信息: $GPU_NAME
GPU内存: ${GPU_MEMORY}MB
测试时间: $(date)
总耗时: ${DURATION}秒 ($(($DURATION / 60))分钟)

配置参数:
- 批次大小: $BATCH_SIZE
- 评估批次大小: $EVAL_BATCH_SIZE
- 最大序列长度: $MAX_SEQ_LEN
- 梯度累积步数: $GRADIENT_ACCUMULATION
- 学习率: $LEARNING_RATE
- Verbalizer学习率: $LR2
- 精度设置: ${USE_BF16:-FP32/FP16}
- 内存分配策略: ${MEMORY_FRACTION}%

环境变量:
- PYTORCH_CUDA_ALLOC_CONF: $PYTORCH_CUDA_ALLOC_CONF
- TORCH_CUDNN_BENCHMARK: ${TORCH_CUDNN_BENCHMARK:-0}
- NVIDIA_TF32_OVERRIDE: ${NVIDIA_TF32_OVERRIDE:-0}

结果文件: $RESULT_FILE
TensorBoard日志: runs/train/
========================================
EOF

echo "性能报告已保存到: performance_report_${CONFIG_TYPE}.txt"

# 显示最终GPU内存使用情况
echo "最终GPU内存使用情况:"
nvidia-smi --query-gpu=memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits
