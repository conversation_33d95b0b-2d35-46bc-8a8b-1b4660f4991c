# -*- coding:utf-8 -*-
import torch
import numpy as np
from transformers import __version__ as transformers_version
import random
from transformers import AutoTokenizer, AutoConfig, AutoModelForCausalLM

# 保留BERT相关导入以备兼容性需要
from transformers import <PERSON><PERSON>oken<PERSON>, BertConfig, Bert<PERSON>or<PERSON>askedLM
from openprompt.plms.mlm import MLMTokenizerWrapper
from openprompt.plms.lm import LMTokenizerWrapper
import argparse

logger = None


def print_info(info, file=None):
    if logger is not None:
        logger.info(info)
    else:
        print(info, file=file)


def parse_args(model="hierCRF"):
    parser = argparse.ArgumentParser("")

    parser.add_argument("--model", type=str, default=model, choices=['hierVerb', 'hierCRF'])
    parser.add_argument("--model_name_or_path", default='/root/autodl-tmp/htc/bert-base-uncased')
    parser.add_argument("--result_file", type=str, default="few_shot_train.txt")
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)

    parser.add_argument("--do_train", default=1, type=int)
    parser.add_argument("--do_dev", default=1, type=bool)
    parser.add_argument("--do_test", default=1, type=bool)

    parser.add_argument("--not_manual", default=False, type=int)
    parser.add_argument("--depth", default=2, type=int)

    parser.add_argument("--device", default=0, type=int)

    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)

    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)

    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)

    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="Max sequence length.")
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument('--mean_verbalizer', default=True, type=bool)

    parser.add_argument("--shot", type=int, default=1)
    parser.add_argument("--seed", type=int, default=171)

    parser.add_argument("--freeze_plm", default=0, type=int)

    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--verbalizer", type=str, default="soft")

    parser.add_argument("--template_id", default=0, type=int)

    parser.add_argument("--multi_label", default=0, type=int)

    parser.add_argument("--early_stop", default=10, type=int)
    parser.add_argument("--eval_full", default=0, type=int)
    if model == "hierVerb":
        parser.add_argument("--use_new_ct", default=1, type=int)
        parser.add_argument("--contrastive_loss", default=1, type=int)
        parser.add_argument("--contrastive_level", default=1, type=int)
        parser.add_argument("--contrastive_alpha", default=0.99, type=float)
        parser.add_argument("--contrastive_logits", default=1, type=int)
        parser.add_argument("--use_dropout_sim", default=1, type=int)
        parser.add_argument("--imbalanced_weight", default=True, type=bool)
        parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
        parser.add_argument("--max_epochs", type=int, default=20)
        parser.add_argument("--constraint_loss", default=1, type=int)
        parser.add_argument("--constraint_alpha", default=-1, type=float)
        parser.add_argument("--cs_mode", default=0, type=int)

        parser.add_argument("--lm_training", default=1, type=int)
        parser.add_argument("--lm_alpha", default=0.999, type=float)

        parser.add_argument("--lr", default=5e-5, type=float)
        parser.add_argument("--lr2", default=1e-4, type=float)

        parser.add_argument("--batch_size", default=5, type=int)
        parser.add_argument("--eval_batch_size", default=20, type=int)

        args = parser.parse_args()
        return args
    elif model == "hierCRF":

        parser.add_argument("--lr", default=5e-5, type=float)
        parser.add_argument("--lr2", default=1e-4, type=float)
        parser.add_argument("--lr3", default=5e-2, type=float)
        parser.add_argument("--max_epochs", type=int, default=50)
        parser.add_argument("--hierCRF_loss", default=1, type=int)

        parser.add_argument("--hierCRF_alpha", default=-1, type=float)
        parser.add_argument("--batch_size", default=10, type=int)
        parser.add_argument("--eval_batch_size", default=20, type=int)

        parser.add_argument("--multi_verb_loss", default=1, type=int)
        parser.add_argument("--multi_verb_loss_alpha", default=-1, type=int)

        parser.add_argument("--lm_training", default=0, type=int)
        parser.add_argument("--lm_alpha", default=0.999, type=float)

        args = parser.parse_args()
        return args
    else:
        raise NotImplementedError


def load_plm_from_config(args, model_path, specials_to_add=None, **kwargs):
    r"""A plm loader using a global config.
    It will load the model, tokenizer, and config simulatenously.
    Now supports both BERT and Qwen3 models.

    Args:
        args: Arguments containing model configuration
        model_path: Path to the pretrained model
        specials_to_add: Special tokens to add (optional)

    Returns:
        :obj:`PreTrainedModel`: The pretrained model.
        :obj:`tokenizer`: The pretrained tokenizer.
        :obj:`model_config`: The config of the pretrained model.
        :obj:`wrapper`: The wrapper class of this plm.
    """
    # 检查是否为Qwen3模型
    if "qwen" in model_path.lower() or "Qwen" in model_path:
        print(f"Loading Qwen3 model from {model_path}")

        # 加载Qwen3配置和模型
        model_config = AutoConfig.from_pretrained(model_path)

        # 设置dropout（如果配置支持）
        if hasattr(model_config, 'hidden_dropout_prob'):
            model_config.hidden_dropout_prob = args.dropout
        elif hasattr(model_config, 'attention_dropout'):
            model_config.attention_dropout = args.dropout

        # A100优化的模型加载配置
        # 检测GPU类型以优化加载策略
        gpu_name = ""
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)

        # A100优化配置
        if "A100" in gpu_name:
            print(f"检测到A100 GPU: {gpu_name}，使用A100优化配置")
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                config=model_config,
                torch_dtype=torch.bfloat16,  # A100原生支持BF16
                device_map=None,  # 让用户手动管理设备
                low_cpu_mem_usage=True,  # A100内存充足，可以启用
                trust_remote_code=True
            )
        else:
            # 通用配置（兼容3090等其他GPU）
            print(f"使用通用GPU配置 (GPU: {gpu_name})")
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                config=model_config,
                torch_dtype=torch.bfloat16,  # Qwen3推荐使用bfloat16
                device_map=None  # 让用户手动管理设备
            )

        # 加载tokenizer - 使用slow tokenizer以兼容openprompt
        tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)

        # 确保tokenizer有pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # 使用LM wrapper而不是MLM wrapper，因为Qwen3是生成式模型
        wrapper = LMTokenizerWrapper

    else:
        # 保持原有的BERT加载逻辑
        print(f"Loading BERT model from {model_path}")
        model_config = BertConfig.from_pretrained(model_path)
        model_config.hidden_dropout_prob = args.dropout
        model = BertForMaskedLM.from_pretrained(model_path, config=model_config)
        tokenizer = BertTokenizer.from_pretrained(model_path)
        wrapper = MLMTokenizerWrapper

    return model, tokenizer, model_config, wrapper


def seed_torch(seed=1029):
    print('Set seed to', seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True


def _mask_tokens(tokenizer, input_ids):
    """ Prepare masked tokens inputs/labels for masked language modeling: 80% MASK, 10% random, 10% original.
    Now supports both BERT and Qwen3 tokenizers.
    """
    labels = input_ids.clone()
    # We sample a few tokens in each sequence for masked-LM training (with probability 0.15)
    probability_matrix = torch.full(labels.shape, 0.15)

    # 处理特殊token mask
    try:
        special_tokens_mask = [tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True) for val in
                               labels.tolist()]
        probability_matrix.masked_fill_(torch.tensor(special_tokens_mask, dtype=torch.bool), value=0.0)
    except:
        # 如果tokenizer不支持get_special_tokens_mask，使用备用方法
        special_token_ids = set()
        if hasattr(tokenizer, 'special_tokens_map'):
            for token in tokenizer.special_tokens_map.values():
                if isinstance(token, str):
                    special_token_ids.add(tokenizer.convert_tokens_to_ids(token))
                elif isinstance(token, list):
                    for t in token:
                        special_token_ids.add(tokenizer.convert_tokens_to_ids(t))

        # 创建特殊token mask
        for i, token_id in enumerate(labels.flatten()):
            if token_id.item() in special_token_ids:
                probability_matrix.flatten()[i] = 0.0

    masked_indices = torch.bernoulli(probability_matrix).bool()

    # if a version of transformers < 2.4.0 is used, -1 is the expected value for indices to ignore
    if [int(v) for v in transformers_version.split('.')][:3] >= [2, 4, 0]:
        ignore_value = -100
    else:
        ignore_value = -1

    labels[~masked_indices] = ignore_value  # We only compute loss on masked tokens

    # 80% of the time, we replace masked input tokens with mask token
    indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices

    # 处理mask token - BERT有mask_token，Qwen3可能没有
    if hasattr(tokenizer, 'mask_token') and tokenizer.mask_token is not None:
        mask_token_id = tokenizer.convert_tokens_to_ids(tokenizer.mask_token)
    else:
        # 对于没有mask_token的tokenizer（如Qwen3），使用unk_token或特殊token
        if hasattr(tokenizer, 'unk_token') and tokenizer.unk_token is not None:
            mask_token_id = tokenizer.convert_tokens_to_ids(tokenizer.unk_token)
        else:
            # 使用一个不常见的token作为mask
            mask_token_id = tokenizer.vocab_size - 1

    input_ids[indices_replaced] = mask_token_id

    # 10% of the time, we replace masked input tokens with random word
    indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
    random_words = torch.randint(len(tokenizer), labels.shape, dtype=torch.long)
    input_ids[indices_random] = random_words[indices_random]

    # The rest of the time (10% of the time) we keep the masked input tokens unchanged
    return input_ids, labels
