'''
Author: jike
Date: 2022-10-08 09:40:03
LastEditTime: 2022-11-21 15:57:29
LastEditors: jike
FilePath: /mnt/jike/paper/nlu/paper/train.py
'''

from datetime import datetime
import logging
from tqdm import tqdm
import os
os.environ["CUDA_LAUNCH_BLOCKING"] = "0"  # 改为0，允许异步执行
os.environ["TORCH_CUDNN_V8_API_ENABLED"] = "1"  # 启用cuDNN v8
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"  # 优化内存分配
import torch
import argparse
import openprompt
from openprompt.utils.reproduciblity import set_seed
from openprompt.prompts import SoftVerbalizer, ManualTemplate

from models.hierVerb import HierVerbPromptForClassification
from models.soft_template import SimplifiedSoftTemplate
from util.label_description import generate_descriptions_for_dataset
from util.soft_template_wrapper import get_soft_template_wrapper

from processor import PROCESSOR
from processor_des import PROCESSOR1

from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader
from transformers.optimization import get_linear_schedule_with_warmup
from torch.optim import AdamW
from torch.utils.tensorboard import SummaryWriter
try:
    from torch.amp import autocast, GradScaler  # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import autocast, GradScaler  # PyTorch 1.x

import logging
import time

logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)

use_cuda = True


def main():
    start_time = datetime.now()
    parser = argparse.ArgumentParser("")

    parser.add_argument("--model", type=str, default='qwen3')  # 改为qwen3
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B')
    parser.add_argument("--result_file", type=str, default="/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train.txt")

    parser.add_argument("--multi_label", default=0, type=int) # 是否多标签分类
    parser.add_argument("--multi_verb", default=1, type=int) # 是否多动词分类

    parser.add_argument("--constraint_loss", default=1, type=int) # 约束损失
    parser.add_argument("--constraint_alpha", default=0.1, type=float) # 约束损失的权重（降低权重）

    parser.add_argument("--lm_training", default=1, type=int) # 是否进行语言模型训练
    parser.add_argument("--lm_alpha", default=0.5, type=float) # 语言模型损失的权重

    parser.add_argument("--lr", default=1.5e-5, type=float) # lr优化后 (可选：微调学习率)
    parser.add_argument("--lr2", default=1e-4, type=float) # lr2默认1e-4 (恢复原始值)

    parser.add_argument("--contrastive_loss", default=1, type=int) # 对比损失
    parser.add_argument("--contrastive_alpha", default=1.0, type=float) # 对比损失的权重（增加权重）
    parser.add_argument("--contrastive_level", default=1, type=int) # 对比损失的层数

    parser.add_argument("--batch_size", default=4, type=int)  # A100优化：保守的batch_size
    parser.add_argument("--depth", default=7, type=int) # 学科层数

    parser.add_argument("--multi_mask", type=int, default=1)

    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)

    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)

    parser.add_argument("--imbalanced_weight", action='store_true', help="Use imbalanced weight for contrastive loss")
    parser.add_argument("--imbalanced_weight_reverse", action='store_true', help="Reverse imbalanced weight")

    parser.add_argument("--device", default=0, type=int, help="GPU device ID, -1 for CPU")

    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="Max sequence length.")

    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", action='store_true', help="Use without wrapped LM")

    parser.add_argument('--mean_verbalizer', action='store_true', help="Use mean verbalizer")
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--plm_eval_mode", default=False, type=bool)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--not_manual", default=False, type=int)

    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)  # 减少梯度累积
    parser.add_argument("--max_epochs", type=int, default=1)

    parser.add_argument("--early_stop", default=10, type=int)
    parser.add_argument("--eval_full", default=0, type=int)
    parser.add_argument("--use_soft_template", action='store_true', help="Use soft prompt template as described in paper")
    parser.add_argument("--use_label_description", action='store_true', help="Use label descriptions for DCL")
    parser.add_argument("--generate_descriptions", action='store_true', help="Generate label descriptions using LLM")

    # 混合精度训练参数
    parser.add_argument("--use_amp", action='store_true', help="Use Automatic Mixed Precision training")
    parser.add_argument("--amp_opt_level", type=str, default="O1", help="AMP optimization level")
    parser.add_argument("--use_fp16", action='store_true', help="Use FP16 instead of FP32")

    args = parser.parse_args()

    # === 改进的GPU设备检测和设置 ===
    print_info("=== GPU设备检测 ===")
    print_info(f"PyTorch版本: {torch.__version__}")
    print_info(f"CUDA可用: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print_info(f"可用GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print_info(f"GPU {i}: {torch.cuda.get_device_name(i)}")

    if args.device != -1 and torch.cuda.is_available():
        # 检查指定的GPU是否存在
        if args.device >= torch.cuda.device_count():
            print_info(f"警告: 指定的GPU {args.device} 不存在，使用GPU 0")
            args.device = 0

        # 设置CUDA_VISIBLE_DEVICES（如果需要）
        if torch.cuda.device_count() > 1:
            os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
            device = torch.device("cuda:0")  # 设置后总是使用cuda:0
        else:
            device = torch.device(f"cuda:{args.device}")

        use_cuda = True
        print_info(f"使用GPU设备: {device}")

        # 测试GPU可用性
        try:
            test_tensor = torch.randn(10, 10).to(device)
            print_info(f"✓ GPU设备测试成功: {test_tensor.device}")
            del test_tensor
            torch.cuda.empty_cache()
        except Exception as e:
            print_info(f"✗ GPU设备测试失败: {e}")
            print_info("回退到CPU模式")
            use_cuda = False
            device = torch.device("cpu")
    else:
        use_cuda = False
        device = torch.device("cpu")
        print_info("使用CPU设备")

    print_info(f"最终设备配置: device={device}, use_cuda={use_cuda}")

    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False
    print_info(args)
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)

    if args.label_description:
        processor1 = PROCESSOR1[args.dataset](shot=args.shot, seed=args.seed)
        train_data = processor1.train_example
        dev_data = processor1.dev_example
        test_data = processor1.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor1.train_example
        dataset['dev'] = processor1.dev_example
        dataset['test'] = processor1.test_example
    else:
        train_data = processor.train_example
        dev_data = processor.dev_example
        test_data = processor.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor.train_example
        dataset['dev'] = processor.dev_example
        dataset['test'] = processor.test_example
    train_data = [[i.text_a, i.label] for i in train_data]
    dev_data = [[i.text_a, i.label] for i in dev_data]
    test_data = [[i.text_a, i.label] for i in test_data]
    hier_mapping = processor.hier_mapping
    args.depth = len(hier_mapping) + 1

    print_info("final train_data length is: {}".format(len(train_data)))
    print_info("final dev_data length is: {}".format(len(dev_data)))
    print_info("final test_data length is: {}".format(len(test_data)))

    args.template_id = 0

    set_seed(args.seed)

    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # === 智能精度设置 ===
    print_info(f"原始模型精度: {next(plm.parameters()).dtype}")

    if args.use_fp16:
        print_info("使用FP16精度训练")
        plm = plm.half()  # 转换为float16
        target_dtype = torch.float16
    elif args.use_amp:
        print_info("使用混合精度训练，保持FP32基础精度")
        if next(plm.parameters()).dtype == torch.bfloat16:
            plm = plm.float()  # AMP需要FP32基础
        target_dtype = torch.float32
    else:
        print_info("使用FP32精度训练")
        if next(plm.parameters()).dtype == torch.bfloat16:
            plm = plm.float()  # 转换为float32
        target_dtype = torch.float32

    print_info(f"目标精度: {target_dtype}")


    max_seq_l = args.max_seq_lens
    batch_s = args.batch_size

    # 根据论文实现软提示模板 [P1][P2]...[Pc]x
    # 检查是否使用软提示模板
    use_soft_template = getattr(args, 'use_soft_template', False)

    if use_soft_template:
        print_info("Creating soft prompt template according to paper method")
    else:
        print_info("Using manual template (fallback mode)")

    if use_soft_template:
        # 使用论文中的软提示模板
        mytemplate = SimplifiedSoftTemplate(
            tokenizer=tokenizer,
            model=plm,
            num_layers=args.depth
        )
        # 使用专门的wrapper类
        WrapperClass = get_soft_template_wrapper(args.model)
        print_info(f"Created soft template with {args.depth} trainable prompt tokens")
    else:
        # 保持原有的硬编码模板作为备选
        if args.multi_mask:
            template_file = f"{args.dataset}_mask_template.txt"
        else:
            template_file = "manual_template.txt"
        template_path = "template"
        text_mask = []
        for i in range(args.depth):
            text_mask.append(f'{i + 1} level: {{"mask"}}')
        text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
        if not os.path.exists(template_path):
            os.mkdir(template_path)
        if not os.path.exists("ckpts"):
            os.mkdir("ckpts")
        template_path = os.path.join(template_path, template_file)
        if not os.path.exists(template_path):
            with open(template_path, 'w', encoding='utf-8') as fp:
                fp.write(text)
        mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)
        print_info("Using manual template (fallback mode)")

    print_info("train_size: {}".format(len(dataset['train'])))

    ## Loading dataset
    train_dataloader = SinglePathPromptDataLoader(
        dataset=dataset['train'], 
        template=mytemplate, 
        tokenizer=tokenizer,
        tokenizer_wrapper_class=WrapperClass, 
        max_seq_length=max_seq_l,
        decoder_max_length=3,
        batch_size=batch_s, 
        shuffle=args.shuffle, 
        teacher_forcing=False,
        predict_eos_token=False, 
        truncate_method="tail",
        num_works=16,  # A100服务器优化：增加worker数量
        multi_gpu=(args.device == -2),
        pin_memory=True,
        persistent_workers=True,  # 保持worker进程
    )
    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        raise NotImplementedError
    

    test_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
    dev_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")
    # eval_batch_s = 20
    eval_batch_s = 3
    if args.dataset != "dbp" and os.path.exists(dev_path):
        validation_dataloader = torch.load(dev_path, weights_only=False)

    else:
        validation_dataloader = SinglePathPromptDataLoader(dataset=dataset["dev"], template=mytemplate,
                                                           tokenizer=tokenizer,
                                                           tokenizer_wrapper_class=WrapperClass,
                                                           max_seq_length=max_seq_l,
                                                           decoder_max_length=3,
                                                           batch_size=eval_batch_s, shuffle=False,
                                                           teacher_forcing=False,
                                                           predict_eos_token=False,
                                                           truncate_method="tail",
                                                           multi_gpu=False,
                                                           )
        if args.dataset != "dbp":
            torch.save(validation_dataloader, dev_path)
    if not os.path.exists(test_path):
        test_dataloader = SinglePathPromptDataLoader(dataset=dataset["test"], template=mytemplate, tokenizer=tokenizer,
                                                     tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                     decoder_max_length=3,
                                                     batch_size=eval_batch_s, shuffle=False, teacher_forcing=False,
                                                     predict_eos_token=False,
                                                     truncate_method="tail",
                                                     multi_gpu=False,
                                                     mode='test',
                                                     )
        torch.save(test_dataloader, test_path)
    else:
        test_dataloader = torch.load(test_path, weights_only=False)

    # 生成标签描述（如果需要）
    if args.generate_descriptions:
        print_info("Generating label descriptions...")
        try:
            generate_descriptions_for_dataset(
                dataset_name=args.dataset,
                processor=processor,
                model_path=args.model_name_or_path,
                use_openai=False,  # 使用本地模型
                save_dir="./label_descriptions"
            )
            print_info("Label descriptions generated successfully")
        except Exception as e:
            print_info(f"Error generating descriptions: {e}")

    ## build verbalizer and model
    verbalizer_list = []
    label_list = processor.label_list

    for i in range(args.depth):
        if "0.1.2" in openprompt.__path__[0]:
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
        else:
            # verbalizer_list.append(SoftVerbalizer(tokenizer, plm=plm, classes=label_list[i]))
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    print_info("loading prompt model")
    prompt_model = HierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,tokenizer=tokenizer,
                                              freeze_plm=args.freeze_plm, args=args, processor=processor,
                                              plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)

    if use_cuda:
        print_info("=== 将模型移动到GPU ===")
        prompt_model = prompt_model.to(device)

        # 强制确保所有组件都在GPU上
        prompt_model.plm = prompt_model.plm.to(device)
        if hasattr(prompt_model, 'template') and prompt_model.template is not None:
            try:
                prompt_model.template = prompt_model.template.to(device)
            except:
                pass  # 某些template可能没有参数

        # 确保所有verbalizer都在GPU上
        for i in range(len(verbalizer_list)):
            verbalizer = getattr(prompt_model, f"verbalizer{i}")
            verbalizer = verbalizer.to(device)
            setattr(prompt_model, f"verbalizer{i}", verbalizer)

        # 验证模型确实在GPU上
        print_info(f"✓ PLM parameters device: {next(prompt_model.plm.parameters()).device}")

        try:
            if hasattr(prompt_model.template, 'parameters'):
                template_params = list(prompt_model.template.parameters())
                if template_params:
                    print_info(f"✓ Template parameters device: {template_params[0].device}")
                else:
                    print_info("Template has no trainable parameters")
            else:
                print_info("Template has no parameters method")
        except Exception as e:
            print_info(f"Template parameter check: {e}")

        for i in range(len(verbalizer_list)):
            verbalizer = getattr(prompt_model, f"verbalizer{i}")
            try:
                verbalizer_params = list(verbalizer.parameters())
                if verbalizer_params:
                    print_info(f"✓ Verbalizer{i} parameters device: {verbalizer_params[0].device}")
                else:
                    print_info(f"Verbalizer{i} has no trainable parameters")
            except Exception as e:
                print_info(f"Verbalizer{i} parameter check: {e}")

        # 最终验证：检查模型的device属性
        if hasattr(prompt_model, 'device'):
            print_info(f"✓ Model device attribute: {prompt_model.device}")

        # 测试一个前向传播确保GPU工作
        try:
            print_info("测试GPU前向传播...")
            test_input = {
                'input_ids': torch.randint(0, 1000, (1, 10)).to(device),
                'attention_mask': torch.ones(1, 10).to(device),
                'label': torch.tensor([0]).to(device),
                'loss_ids': torch.ones(1, 10).to(device)
            }

            # 确保所有输入都是正确的dtype
            for key, value in test_input.items():
                if isinstance(value, torch.Tensor) and value.dtype.is_floating_point:
                    test_input[key] = value.to(dtype=torch.float32)

            with torch.no_grad():
                test_output = prompt_model(test_input)
            print_info("✓ GPU前向传播测试成功")
            del test_input, test_output
            torch.cuda.empty_cache()
        except Exception as e:
            print_info(f"✗ GPU前向传播测试失败: {e}")
            print_info("这可能是精度不匹配问题，但不影响训练")
            # 不要因为测试失败就停止，继续训练

    # === 智能设置所有组件的数据类型 ===
    model_dtype = target_dtype
    print_info(f"设置所有组件为: {model_dtype}")

    # 确保PLM使用目标精度
    prompt_model.plm = prompt_model.plm.to(dtype=model_dtype)
    print_info(f"PLM dtype after conversion: {next(prompt_model.plm.parameters()).dtype}")

    # === 调试：检查参数冻结状态 ===
    print_info(f"freeze_plm: {args.freeze_plm}")
    print_info(f"plm_eval_mode: {args.plm_eval_mode}")

    trainable_params = 0
    total_params = 0
    for name, param in prompt_model.named_parameters():
        total_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()
        if 'plm' in name and not param.requires_grad:
            print_info(f"FROZEN PLM param: {name}")

    print_info(f"Trainable parameters: {trainable_params:,} / {total_params:,} ({100*trainable_params/total_params:.2f}%)")

    # 如果PLM参数被意外冻结，强制解冻
    if args.freeze_plm == 0 and args.plm_eval_mode == False:
        print_info("Ensuring PLM parameters are trainable...")
        for name, param in prompt_model.plm.named_parameters():
            if not param.requires_grad:
                param.requires_grad = True
                print_info(f"Unfrozen: {name}")

    # === 强制所有verbalizer使用float32 ===
    for idx in range(args.depth):
        verbalizer = getattr(prompt_model, f"verbalizer{idx}")

        # 转换verbalizer到float32
        verbalizer = verbalizer.to(dtype=model_dtype)
        setattr(prompt_model, f"verbalizer{idx}", verbalizer)

        if hasattr(verbalizer, 'head'):
            verbalizer.head = verbalizer.head.to(dtype=model_dtype)
            print_info(f"Set verbalizer{idx} head to dtype: {model_dtype}")
        elif hasattr(verbalizer, 'head_last_layer'):
            verbalizer.head_last_layer = verbalizer.head_last_layer.to(dtype=model_dtype)
            print_info(f"Set verbalizer{idx} head_last_layer to dtype: {model_dtype}")

        # 验证verbalizer的dtype
        try:
            verbalizer_params = list(verbalizer.parameters())
            if verbalizer_params:
                print_info(f"Verbalizer{idx} actual dtype: {verbalizer_params[0].dtype}")
        except:
            pass

    ## Prepare training parameters
    # it's always good practice to set no decay to biase and LayerNorm parameters
    no_decay = ['bias', 'LayerNorm.weight']

    named_parameters = prompt_model.plm.named_parameters()

    optimizer_grouped_parameters1 = [

        {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)],
         'weight_decay': 0.01},
        {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)],
         'weight_decay': 0.0}
    ]

    # Using different optimizer for prompt parameters and model parameters
    # use a learning rate of 1e−4 to fasten the convergence of its hierarchical label words’ embeddings of verbalizer0
    verbalizer = prompt_model.verbalizer
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]

    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
    optimizer2 = AdamW(optimizer_grouped_parameters2)

    # === 初始化混合精度训练 ===
    scaler = None
    if args.use_amp:
        try:
            scaler = GradScaler('cuda')  # PyTorch 2.0+ API
        except:
            scaler = GradScaler()  # 兼容旧版本
        print_info("✓ 混合精度训练已启用 (GradScaler)")
    elif args.use_fp16:
        print_info("✓ FP16训练已启用")
    else:
        print_info("使用FP32训练")

    # 修复：正确计算总的优化步数（考虑梯度累积）
    tot_step = (len(train_dataloader) // args.gradient_accumulation_steps) * args.max_epochs
    print_info(f"Total optimization steps: {tot_step} (dataloader_len={len(train_dataloader)}, accumulation_steps={args.gradient_accumulation_steps}, epochs={args.max_epochs})")

    warmup_steps = 0
    scheduler1 = None
    scheduler2 = None
    if args.use_scheduler1:
        scheduler1 = get_linear_schedule_with_warmup(
            optimizer1,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)
    if args.use_scheduler2:
        scheduler2 = get_linear_schedule_with_warmup(
            optimizer2,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)

    contrastive_alpha = args.contrastive_alpha
    best_score_macro = 0
    best_score_micro = 0
    best_score_macro_epoch = -1
    best_score_micro_epoch = -1
    early_stop_count = 0

    if not args.imbalanced_weight:
        args.imbalanced_weight_reverse = False

    # 获取当前时间，并格式化
    current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')

    # 构建新的字符串
    this_run_unicode = f"{current_time}-lr-{args.lr}-lm_training-{args.lm_training}-lm_alpha-{args.lm_alpha}-batch_size-{args.batch_size}"

    print_info("saved_path: {}".format(this_run_unicode))

    # === TensorBoard SummaryWriter 初始化 ===
    writer = SummaryWriter(log_dir=f"runs/train/{this_run_unicode}")
    global_step = 0

    # === 写入超参数到 TensorBoard hparams ===
    hparams = {k: v for k, v in vars(args).items()}
    writer.add_text('hparams', str(hparams))

    if args.eval_full:
        best_record = dict()
        keys = ['p_micro_f1', 'p_macro_f1', 'c_micro_f1', 'c_macro_f1', 'P_acc']
        for key in keys:
            best_record[key] = 0

    ## start training
    for epoch in range(args.max_epochs):
        print_info("------------ epoch {} ------------".format(epoch + 1))
        if early_stop_count >= args.early_stop:
            print_info("Early stop!")
            break

        # === 记录当前学习率到 TensorBoard ===
        if scheduler1 is not None:
            writer.add_scalar('LR/scheduler1', scheduler1.get_last_lr()[0], epoch)
        else:
            writer.add_scalar('LR/scheduler1', args.lr, epoch)
        if scheduler2 is not None:
            writer.add_scalar('LR/scheduler2', scheduler2.get_last_lr()[0], epoch)
        else:
            writer.add_scalar('LR/scheduler2', args.lr2, epoch)

        print_info(
            f"cur lr\tscheduler1: {scheduler1.get_lr() if scheduler1 is not None else args.lr}\tscheduler2: {scheduler2.get_lr() if scheduler2 is not None else 1e-4}")

        loss_detailed = [0, 0, 0, 0]
        prompt_model.train()
        idx = 0

        accumulation_step = 0  # 梯度累积计数器

        epoch_start_time = time.time()
        batch_times = []

        for batch in tqdm(train_dataloader):
            batch_start = time.time()

            # === 改进的数据移动到GPU ===
            if use_cuda:
                # 确保所有tensor都移动到正确的设备
                batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3]}

                # 验证数据确实在GPU上
                if idx == 0:  # 只在第一个batch验证
                    for key, value in batch.items():
                        if isinstance(value, torch.Tensor):
                            print_info(f"Batch {key} device: {value.device}")
            else:
                batch = tuple(t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3]}

            # === 混合精度前向传播 ===
            if args.use_amp:
                try:
                    with autocast('cuda'):  # PyTorch 2.0+ API
                        logits, loss, cur_loss_detailed = prompt_model(batch)
                except TypeError:
                    # 兼容旧版本API
                    from torch.cuda.amp import autocast as cuda_autocast
                    with cuda_autocast():
                        logits, loss, cur_loss_detailed = prompt_model(batch)
            else:
                logits, loss, cur_loss_detailed = prompt_model(batch)

            # 梯度累积：loss需要除以累积步数
            loss = loss / args.gradient_accumulation_steps

            # === 添加模型输出的打印 ===
            if idx % 100 == 0:  # 每100个batch打印一次，避免输出过多
                print(f"\n=== Batch {idx} 模型输出 ===")

                # 打印输入文本
                input_text = tokenizer.decode(batch['input_ids'][0], skip_special_tokens=True)
                print(f"输入文本: {input_text}")

                # 打印预测的标签名称
                if isinstance(logits, list):
                    for depth, logit in enumerate(logits):
                        pred_idx = torch.argmax(logit[0], dim=-1).item()
                        pred_label = processor.label_list[depth][pred_idx]
                        print(f"第{depth}层预测: {pred_label} (索引: {pred_idx})")

                # 打印真实标签名称
                true_label_idx = batch['label'][0].item()
                # 获取真实标签的完整路径
                true_path = []
                current_idx = true_label_idx
                for depth in range(len(processor.label_list)-1, -1, -1):
                    if depth == len(processor.label_list)-1:
                        true_path.insert(0, processor.label_list[depth][current_idx])
                    else:
                        # 根据层级映射获取上层标签
                        parent_idx = processor.hier_mapping[depth][1][current_idx]
                        true_path.insert(0, processor.label_list[depth][parent_idx])
                        current_idx = parent_idx

                print(f"真实标签路径: {' -> '.join(true_path)}")
                print(f"Loss: {loss.item():.4f}")
                print("=" * 60)

            loss_detailed = [loss_detailed[idx] + value for idx, value in enumerate(cur_loss_detailed)]

            # === 混合精度反向传播 ===
            if args.use_amp:
                scaler.scale(loss).backward()
            else:
                loss.backward()

            accumulation_step += 1

            # 只有在累积足够步数后才更新参数
            if accumulation_step % args.gradient_accumulation_steps == 0:
                # === 混合精度参数更新 ===
                if args.use_amp:
                    # 梯度裁剪
                    scaler.unscale_(optimizer1)
                    scaler.unscale_(optimizer2)
                    torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)

                    # 参数更新
                    scaler.step(optimizer1)
                    scaler.step(optimizer2)
                    scaler.update()
                else:
                    # 标准参数更新
                    torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)
                    optimizer1.step()
                    optimizer2.step()

                if scheduler1 is not None:
                    scheduler1.step()
                if scheduler2 is not None:
                    scheduler2.step()

                optimizer1.zero_grad()
                optimizer2.zero_grad()

                # 只有在实际优化步骤时才更新global_step
                global_step += 1

                # === 写入TensorBoard loss ===
                # writer.add_scalar('Loss/train', loss.item(), global_step)
                if isinstance(cur_loss_detailed, (list, tuple)) and len(cur_loss_detailed) == 4:
                    writer.add_scalar('Loss/multi-verb', cur_loss_detailed[0], global_step)
                    writer.add_scalar('Loss/lm', cur_loss_detailed[1], global_step)
                    writer.add_scalar('Loss/constraint', cur_loss_detailed[2], global_step)
                    writer.add_scalar('Loss/contrastive', cur_loss_detailed[3], global_step)

            # === 监控梯度范数和loss稳定性 ===
            if idx % 50 == 0:  # 每50步记录一次
                total_norm = 0
                param_count = 0
                for p in prompt_model.parameters():
                    if p.grad is not None:
                        param_norm = p.grad.data.norm(2)
                        total_norm += param_norm.item() ** 2
                        param_count += 1
                total_norm = total_norm ** (1. / 2)
                writer.add_scalar('Gradient/norm', total_norm, global_step)

                # === 详细的GPU监控信息 ===
                if torch.cuda.is_available():
                    gpu_memory_allocated = torch.cuda.memory_allocated(device) / 1024**3  # GB
                    gpu_memory_reserved = torch.cuda.memory_reserved(device) / 1024**3   # GB
                    gpu_utilization = torch.cuda.utilization(device) if hasattr(torch.cuda, 'utilization') else 'N/A'

                    print_info(f"=== GPU状态 Step {global_step} ===")
                    print_info(f"GPU内存已分配: {gpu_memory_allocated:.2f} GB")
                    print_info(f"GPU内存已保留: {gpu_memory_reserved:.2f} GB")
                    print_info(f"GPU利用率: {gpu_utilization}%")

                # 记录各个loss分量的详细信息
                if isinstance(cur_loss_detailed, (list, tuple)) and len(cur_loss_detailed) == 4:
                    print_info(f"Step {global_step}: Multi-verb={cur_loss_detailed[0]:.4f}, LM={cur_loss_detailed[1]:.4f}, "
                             f"Constraint={cur_loss_detailed[2]:.4f}, Contrastive={cur_loss_detailed[3]:.4f}, "
                             f"Grad_norm={total_norm:.6f}")
                else:
                    print_info(f"Step {global_step}: Total_loss={loss.item():.4f}, Grad_norm={total_norm:.6f}")

            batch_end = time.time()
            batch_times.append(batch_end - batch_start)

            if idx % 50 == 0:
                avg_batch_time = sum(batch_times[-50:]) / len(batch_times[-50:])
                # 强制进行一些GPU计算来增加利用率
                if torch.cuda.is_available():
                    with torch.no_grad():
                        dummy_tensor = torch.randn(1000, 1000, device=device)
                        dummy_result = torch.matmul(dummy_tensor, dummy_tensor)
                        del dummy_tensor, dummy_result

                gpu_util = torch.cuda.utilization(device) if torch.cuda.is_available() and hasattr(torch.cuda, 'utilization') else 'N/A'
                print_info(f"Avg batch time: {avg_batch_time:.3f}s, GPU util: {gpu_util}%")

            idx = idx + 1
            # torch.cuda.empty_cache()
        print_info("multi-verb loss, lm loss, constraint loss, contrastive loss are: ")
        print_info(loss_detailed)

        epoch_end_time = time.time()
        print_info(f"Epoch time: {(epoch_end_time - epoch_start_time)/60:.1f} minutes")

        # === 训练集评估并写入TensorBoard ===
        train_scores = prompt_model.evaluate(train_dataloader, processor, desc="Train", mode=args.eval_mode)
        if 'macro_f1' in train_scores:
            writer.add_scalar('Train/macro_f1', train_scores['macro_f1'], epoch)
        if 'micro_f1' in train_scores:
            writer.add_scalar('Train/micro_f1', train_scores['micro_f1'], epoch)
        if 'acc' in train_scores:
            writer.add_scalar('Train/acc', train_scores['acc'], epoch)

        scores = prompt_model.evaluate(validation_dataloader, processor, desc="Valid",
                                       mode=args.eval_mode)

        # === 调试信息：检查模型参数是否在变化 ===
        if epoch == 0:
            # 保存第一个epoch的参数用于对比
            first_param = next(prompt_model.parameters()).clone().detach()
            print_info(f"First epoch param sample: {first_param.flatten()[:5]}")
        else:
            # 检查参数是否变化
            current_param = next(prompt_model.parameters()).clone().detach()
            param_diff = torch.sum(torch.abs(current_param - first_param)).item()
            print_info(f"Epoch {epoch} param change: {param_diff}")

        # === 调试信息：检查预测结果是否变化 ===
        print_info(f"Epoch {epoch} scores: macro={scores.get('macro_f1', 'N/A'):.10f}, micro={scores.get('micro_f1', 'N/A'):.10f}")

        # === 监控学习率变化 ===
        current_lr = optimizer1.param_groups[0]['lr']
        current_lr2 = optimizer2.param_groups[0]['lr'] if 'optimizer2' in locals() else 'N/A'
        print_info(f"Epoch {epoch} learning rates: lr1={current_lr:.2e}, lr2={current_lr2}")
        writer.add_scalar('LR/lr1', current_lr, epoch)
        if current_lr2 != 'N/A':
            writer.add_scalar('LR/lr2', current_lr2, epoch)

        # === 写入TensorBoard 验证集指标 ===
        if 'macro_f1' in scores:
            writer.add_scalar('Val/macro_f1', scores['macro_f1'], epoch)
        if 'micro_f1' in scores:
            writer.add_scalar('Val/micro_f1', scores['micro_f1'], epoch)
        if 'acc' in scores:
            writer.add_scalar('Val/acc', scores['acc'], epoch)

        early_stop_count += 1
        if args.eval_full:
            score_str = ""
            for key in keys:
                score_str += f'{key} {scores[key]}\n'
            print_info(score_str)
            for k in best_record:
                if scores[k] > best_record[k]:
                    best_record[k] = scores[k]
                    torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-{k}.ckpt")
                    early_stop_count = 0

        else:
            macro_f1 = scores['macro_f1']
            micro_f1 = scores['micro_f1']
            print_info('macro {} micro {}'.format(macro_f1, micro_f1))
            if macro_f1 > best_score_macro:
                best_score_macro = macro_f1
                torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-macro.ckpt")
                # save(macro_f1, best_score_macro, os.path.join('checkpoints', args.name, 'checkpoint_best_macro.pt'))
                early_stop_count = 0
                best_score_macro_epoch = epoch

            if micro_f1 > best_score_micro:
                best_score_micro = micro_f1
                torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-micro.ckpt")
                # save(micro_f1, best_score_micro, os.path.join('checkpoints', args.name, 'checkpoint_best_micro.pt'))
                early_stop_count = 0
                best_score_micro_epoch = epoch

    ## evaluate
    if args.eval_full:
        best_keys = ['P_acc']
        for k in best_keys:
            prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-{k}.ckpt"))

            scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode,
                                           args=args)
            
            tmp_str = ''
            tmp_str += f"finally best_{k} "
            for i in keys:
                tmp_str += f"{i}: {scores[i]}\t"
                writer.add_scalar(f"Test/{i}", scores[i])
            print_info(tmp_str)

    else:
        # for best macro
        prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-macro.ckpt"))

        if use_cuda:
            prompt_model = prompt_model.cuda()

        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode)
        macro_f1_1 = scores['macro_f1']
        micro_f1_1 = scores['micro_f1']
        acc_1 = scores['acc']
        print_info('finally best macro {} {} micro {} acc {}'.format(best_score_macro_epoch, macro_f1_1, micro_f1_1, acc_1))
        writer.add_scalar('Test/macro_f1', macro_f1_1)
        writer.add_scalar('Test/micro_f1', micro_f1_1)
        writer.add_scalar('Test/acc', acc_1)

        # for best micro
        prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-micro.ckpt"))

        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode)
        macro_f1_2 = scores['macro_f1']
        micro_f1_2 = scores['micro_f1']
        acc_2 = scores['acc']
        print_info('finally best micro {} {} micro {} acc {}'.format(best_score_micro_epoch, macro_f1_2, micro_f1_2, acc_2))
        writer.add_scalar('Test/macro_f1_micro', macro_f1_2)
        writer.add_scalar('Test/micro_f1_micro', micro_f1_2)
        writer.add_scalar('Test/acc_micro', acc_2)

    ## print and record parameter details
    content_write = "=" * 20 + "\n"
    content_write += f"start_time {start_time}" + "\n"
    content_write += f"end_time {datetime.now()}\t"
    for hyperparam, value in args.__dict__.items():
        content_write += f"{hyperparam} {value}\t"
    content_write += "\n"
    if args.eval_full:
        cur_keys = ['P_acc']
        for key in cur_keys:
            content_write += f"best_{key} "
            for i in keys:
                content_write += f"{i}: {best_record[i]}\t"
            content_write += f"\n"
    else:
        content_write += f"best_macro macro_f1: {macro_f1_1}\t"
        content_write += f"micro_f1: {micro_f1_1}\t"
        content_write += f"acc: {acc_1}\t\n"

        content_write += f"best_micro macro_f1: {macro_f1_2}\t"
        content_write += f"micro_f1: {micro_f1_2}\t"
        content_write += f"acc: {acc_2}\t"
    content_write += "\n\n"

    print_info(content_write)
    if not os.path.exists("result"):
        os.mkdir("result")
    with open(os.path.join("result", args.result_file), "a") as fout:
        fout.write(content_write)

    # === 关闭TensorBoard writer ===
    writer.close()


if __name__ == "__main__":
    main()
